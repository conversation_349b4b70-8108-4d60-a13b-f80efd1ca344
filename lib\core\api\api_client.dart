import 'package:dio/dio.dart';

/// Central API client for handling HTTP requests
/// This class provides a configured Dio instance with interceptors
class ApiClient {
  static const String baseUrl = 'https://kairos-mobile.sensoft-labs.com/api';
  late final Dio _dio;
  
  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) {
          // TODO: Use proper logging instead of print
          print(object);
        },
      ),
    );
    
    // TODO: Add authentication interceptor
    // TODO: Add error handling interceptor
  }
  
  Dio get dio => _dio;
  
  // Common HTTP methods
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }
  
  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }
  
  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }
  
  Future<Response> delete(String path) {
    return _dio.delete(path);
  }
}
